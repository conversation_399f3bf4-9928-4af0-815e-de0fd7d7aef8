[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ratatouille"
version = "0.1.0"
description = "A neuroscience data analysis framework for hierarchical calcium imaging and behavioral data processing"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "<PERSON><PERSON> Ma", email = "<EMAIL>"}
]
maintainers = [
    {name = "Xiaoyang Ma", email = "<EMAIL>"}
]
classifiers = [
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
]
requires-python = ">=3.11"
dependencies = [
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "scipy>=1.10.0",
    "opencv-python>=4.7.0",
    "tqdm>=4.65.0",
    "requests>=2.28.0",
    "send2trash>=1.8.0",
    "typing-extensions>=4.5.0",
]

[tool.setuptools.packages.find]
include = ["kitchen*"]
exclude = ["cook*", "critic*", "cuisine*", "ingredients*"]
