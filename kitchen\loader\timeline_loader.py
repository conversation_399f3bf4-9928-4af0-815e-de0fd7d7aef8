import os
import re
import os.path as path
from typing import Generator, <PERSON><PERSON>
import logging
import numpy as np
import pandas as pd

from kitchen.configs import routing
from kitchen.settings.loaders import DATA_HODGEPODGE_MODE, LOADER_STRICT_MODE, MATT_NAME_STYLE_FLAG
from kitchen.structure.hierarchical_data_structure import Fov
from kitchen.structure.neural_data_structure import Timeline


logger = logging.getLogger(__name__)

def timeline_loader_from_fov(fov_node: Fov, timeline_loader_name: str = "default") -> Generator[Tuple[str, str, Timeline], None, None]:
    """
    Load timeline from fov node.

    All custom io function should follow the same strucutre:
    1. Find all timeline files. [Implement DATA_HODGEPODGE_MODE if necessary]
    2. For each timeline file, extract day name, session name, and timeline data.
    3. Yield day name, session name, and timeline data.

    Note:
    day name and session name should be consistent with other data files, like behavior, fluorescence, etc.
    """
    
    def io_default(dir_path: str) -> Generator[<PERSON><PERSON>[str, str, Timeline], None, None]:      
        """Find all timeline files.""" 
        if DATA_HODGEPODGE_MODE:
            timeline_filepaths = routing.search_pattern_file(pattern="TIMELINE_*.csv", search_dir=dir_path)
        else:
            timeline_filepaths = routing.search_pattern_file(pattern="TIMELINE_*.csv", search_dir=path.join(dir_path, "timeline"))
        
        assert len(timeline_filepaths) > 0, f"Cannot find timeline path: {dir_path}"            
            
        for filepath in timeline_filepaths:
            dirname, filename = path.split(filepath)
            if filename.startswith("TIMELINE_") and filename.endswith(".csv"):
                """Extract day name, session name, and timeline data."""
                data_array = pd.read_csv(path.join(dirname, filename), header=0)
                assert 'time' in data_array.columns and 'details' in data_array.columns, \
                      f"Cannot find 'time' and 'details' columns in {filename}"
                
                # extract the string between prefix "TIMELINE_" and postfix ".csv"
                session_name = re.search(r"TIMELINE_(.*)\.csv", filename)
                assert session_name is not None, f"Cannot extract session name from {filename}"
                session_name = session_name.group(1)
                if MATT_NAME_STYLE_FLAG:                    
                    day_name = filename.split("_")[2]
                    assert day_name.startswith("D"), f"Expected day name to start with 'D' in {filename}"
                    day_name = day_name[1:].zfill(2)
                else:
                    day_name = filename.split("_")[1]
                
                extracted_timeline = Timeline(
                    v=data_array['details'].to_numpy(),
                    t=data_array['time'].to_numpy(dtype=np.float32) / 1000
                )
                """Yield day name, session name, and timeline data."""
                yield day_name, session_name, extracted_timeline
        

    def io_old(dir_path: str) -> Generator[Tuple[str, str, Timeline], None, None]:        
        timeline_dir_path = path.join(dir_path, "timeline")
        assert path.exists(timeline_dir_path), f"Cannot find timeline path: {timeline_dir_path}"    

        for filename in os.listdir(timeline_dir_path):
            if filename.startswith("Arduino time point") and filename.endswith(".xlsx"):
                data_array = pd.ExcelFile(path.join(timeline_dir_path, filename))
                for sheet_id, sheet_name in enumerate(data_array.sheet_names):
                    df = data_array.parse(sheet_name, header=None).to_numpy()
                    assert df.shape[1] == 5, f"Cannot find 4 columns in {filename} {sheet_name}"
                    extracted_timeline = Timeline(
                        v=df[:, 0],
                        t=np.array(df[:, 4] / 1000, dtype=np.float32)
                    )
                    day_id = str(sheet_id // 2)
                    yield f"{day_id}", f"{sheet_id}", extracted_timeline


    def io_classic(dir_path: str) -> Generator[Tuple[str, str, Timeline], None, None]:
        timeline_type = pd.ExcelFile(path.join(dir_path, "Arduino.xlsx"))
        timeline_t = pd.ExcelFile(path.join(dir_path, "Arduino time point.xlsx"))

        # sheet_0_column = timeline_t.parse(timeline_t.sheet_names[0], header=0).columns[0]
        # sheet_1_column = timeline_t.parse(timeline_t.sheet_names[1], header=0).columns[0]
        # duplicate_flag =  sheet_0_column == sheet_1_column
        
        # if duplicate_flag:
        #     timeline_t_sheet_name = timeline_t.sheet_names[::2] if len(timeline_t.sheet_names) == 32 else timeline_t.sheet_names
        #     timeline_type_sheet_name = timeline_type.sheet_names[::2] if len(timeline_type.sheet_names) == 32 else timeline_type.sheet_names
        #     assert len(timeline_type_sheet_name) == len(timeline_t_sheet_name) == 16, \
        #         f"Cannot match timeline type and timeline t sheet names in {dir_path}, \
        #          got {timeline_type.sheet_names} and {timeline_t.sheet_names}"
        # else:
        timeline_t_sheet_name = [sheet_name for sheet_name in timeline_t.sheet_names for _ in range(2)] if len(timeline_t.sheet_names) == 16 else timeline_t.sheet_names
        timeline_type_sheet_name = [sheet_name for sheet_name in timeline_type.sheet_names for _ in range(2)] if len(timeline_type.sheet_names) == 16 else timeline_type.sheet_names
        assert len(timeline_type_sheet_name) == len(timeline_t_sheet_name) == 32, \
            f"Cannot match timeline type and timeline t sheet names in {dir_path}, \
                got {timeline_type.sheet_names} and {timeline_t.sheet_names}"
            
        for sheet_id, (type_sheet_name, t_sheet_name) in enumerate(zip(timeline_type_sheet_name, timeline_t_sheet_name)):
            df_type = timeline_type.parse(type_sheet_name, header=None).to_numpy()
            for i, type_value in enumerate(df_type[:, 0]):
                if type_value.lower() in ("puff", "real"):
                    df_type[i, 0] = "Puff"
                elif type_value.lower() in ("blank", "fake"):
                    df_type[i, 0] = "Blank"
                else:
                    raise ValueError(f"Unknown timeline type {type_value} in {type_sheet_name} at {dir_path}")
            df_t = timeline_t.parse(t_sheet_name, header=0).to_numpy()
            try:
                assert df_type.shape[1] >= 1, f"Cannot find 1 column in {type_sheet_name} at {dir_path}"
                assert df_t.shape[1] == 2, f"Cannot find 2 columns in {t_sheet_name} at {dir_path}"
                assert abs(df_type.shape[0] - df_t.shape[0]) <= 1, f"Cannot match timeline type and timeline t in {dir_path}, got {df_type} and {df_t}"
                min_length = min(df_type.shape[0], df_t.shape[0])
                df_type = df_type[:min_length, 0]
                df_t = df_t[:min_length, 0]
                extracted_timeline = Timeline(
                    v=df_type,
                    t=np.array(df_t / 1000, dtype=np.float32)
                )
            except Exception as e:
                logger.debug(f"Error loading timeline in {dir_path} at {type_sheet_name} and {t_sheet_name}: {e}")
                extracted_timeline = Timeline(v=np.array([]), t=np.array([]))
            day_id = str(sheet_id // 2)
            yield f"{day_id}".zfill(2), f"{sheet_id}".zfill(2), extracted_timeline

 

    """Load timeline from fov node."""
    timeline_loader_options = {
        "default": io_default,
        "old": io_old,
        "classic": io_classic,
    }
    default_fov_data_path = routing.default_data_path(fov_node)

    loader_to_use = timeline_loader_options.get(timeline_loader_name)
    if loader_to_use is None:
        raise ValueError(f"Unknown timeline loader: {timeline_loader_name}. Available options: {timeline_loader_options.keys()}")
    
    yield from timeline_loader_options[timeline_loader_name](default_fov_data_path)
    