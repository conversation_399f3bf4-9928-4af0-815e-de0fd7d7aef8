# Ratatouille

A neuroscience data analysis framework for hierarchical calcium imaging and behavioral data processing.

> **_IMPORTANT:_** This readme file is generated by LLM. I'm not sure about its accuracy. 🤖

## Overview

Ratatouille is designed to handle neuroscience experiments involving calcium imaging, electrophysiological recordings, behavioral tracking, and timeline synchronization.

**Core Philosophy**: Data flows through a well-defined hierarchy (Cohort → Mice → FOV → Session → Cell → Trial) where each level encapsulates appropriate temporal and spatial constraints. This design ensures data integrity while providing flexible access patterns for different analysis needs.

For example (see `kitchen/structure/hierarchical_data_structure.py`):

```python
"""
                                    Time →
                           ┌───────────────────────────────────────────────────────────────────┐
                           │     Session 1     │     Session 2     │  ...  │     Session N     │
        ┌──────────────────┼───────────────────────────────────────────────────────────────────┤
        │ FOV 1  Cell 1~K  │                    ░░░░░░░ GCaMP activity (FOV 1) ░░░░░░░░        │
        │ FOV 2  Cell 1~K  │                                                                   │
        │ ...              │                                                                   │
        └──────────────────┴───────────────────────────────────────────────────────────────────┘
                                    │■■■■■■■■■■■■■■ Behavior Data (All FOVs) ■■■■■■■■■■■■■■│
"""
```

## Architecture & Structure

### Directory Organization

```text
├── kitchen/          # Core analysis framework
│   ├── loader/       # Data loading and integration pipeline
│   ├── structure/    # Hierarchical data structures and validation
│   ├── plotter/      # Four-layer plotting pipeline for multi-modal visualization
│   │   ├── macros/       # High-level plotting workflows (session overviews, trial averages)
│   │   ├── decorators/   # Figure styling, layout management, and coordinate systems
│   │   ├── ax_plotter/   # Axis-level data synchronization and plot coordination
│   │   └── unit_plotter/ # Individual data modality rendering (fluorescence, behavior, events)
│   ├── video/        # Video processing and feature extraction
│   ├── operator/     # Data manipulation and synchronization
│   ├── configs/      # Path routing and configuration management
│   ├── settings/     # Module-specific parameters and constants
│   ├── utils/        # Utility functions and algorithms
│   └── writer/       # Data export and reporting utilities
├── cook/             # Analysis pipelines and experimental scripts
├── ingredients/      # Raw experimental data storage
├── cuisine/          # Analysis outputs, reports, and figures
└── critic/           # Comprehensive test suite
```

### Core Components

**Data Structures** (`kitchen/structure/`):

- `hierarchical_data_structure.py`: Node classes for each hierarchy level with coordinate validation
- `neural_data_structure.py`: Time series, events, fluorescence, and timeline data containers
- `meta_data_structure.py`: Coordinate system for temporal and spatial organization

**Data Loading** (`kitchen/loader/`):

- `two_photon_loader.py`: Main entry point for loading calcium imaging experimental hierarchies
- `ephys_loader.py`: Main entry point for loading electrophysiological experimental hierarchies
- `fluorescence_loader.py`: Suite2p/Fall.mat integration with TTL synchronization
- `potential_loader.py`: Electrophysiological data loading with spike detection and filtering
- `behavior_loader.py`: Multi-modal behavioral data processing (lick, locomotion, pupil, etc.)
- `timeline_loader.py`: Event timeline parsing and validation

## Quick Start

### Installation

```bash
pip install -e .
```

### Requirements

- Python ≥3.11
- NumPy ≥1.24.0, Pandas ≥2.0.0, SciPy ≥1.10.0
- OpenCV ≥4.7.0 (for video processing)
- Additional dependencies in `pyproject.toml`

### Basic Usage

```python
# For calcium imaging experiments
import kitchen.loader.two_photon_loader as hier_loader
dataset = hier_loader.cohort_loader(
    template_id="RandPuff",
    cohort_id="HighFreqImaging_202507"
)

# For electrophysiology experiments
import kitchen.loader.ephys_loader as ephys_loader
dataset = ephys_loader.cohort_loader(
    template_id="PassivePuff_JuxtaCelluar_FromJS",
    cohort_id="SST_EXAMPLE"
)

# Generate comprehensive status report
dataset.status(save_path="status_report.xlsx")

# Access specific hierarchy levels
fov_nodes = dataset.select("fov")
trial_nodes = dataset.select("trial")

# Quick diagnostic loading (FOV level only)
diagnostic_data = hier_loader.naive_loader(
    template_id="RandPuff",
    cohort_id="HighFreqImaging_202507"
)
```

## Data Processing Pipeline

### Hierarchical Loading Flow

The data loading process follows a strict hierarchical progression that mirrors experimental organization:

```text
Cohort → Mice → FOV → Session → CellSession → Trial
                ↓
            FovDay (temporal aggregation)
```

### Data Integration Process

Each session integrates three primary data streams:

**Fluorescence Data** (from Suite2p/Fall.mat):

- Raw fluorescence traces (F) with neuropil subtraction (0.7 × Fneu)
- FOV motion correction vectors (xoff, yoff)
- Cell spatial coordinates and selection masks
- Automatic session splitting and temporal alignment

**Electrophysiological Data** (from pickle files):

- Membrane potential recordings with configurable filtering
- Automatic spike detection with customizable thresholds
- Multi-bandwidth component analysis (0.5Hz - 1kHz)
- Support for juxtacellular, whole-cell, and patch-clamp recordings

**Behavioral Data** (from CSV/video files):

- **Lick Events**: Timestamped licking with inactivation window filtering
- **Locomotion**: Rotary encoder data converted to position and velocity
- **Pupil Tracking**: Video-extracted pupil diameter with normalization
- **Whisker Motion**: Optical flow analysis of whisker movement
- **Tongue Tracking**: Video-based tongue position monitoring

**Timeline Events** (from Arduino/timeline files):

- Stimulus delivery timestamps (puffs, tones, rewards)
- Task structure markers (start/end, trial boundaries)

### Synchronization & Alignment

**TTL-Based Synchronization**:

- Primary: TTL signals align fluorescence to behavioral timeline
- Fallback: Fixed offset alignment when TTL data is unavailable
- Validation: Cross-modal timestamp consistency checking

**Event-Based Alignment**:

- Trial extraction relative to stimulus events
- Configurable alignment windows (default: -4s to +8s)
- Support for multiple alignment event types per experiment

### Data Validation & Quality Control

**Coordinate Validation**:

- Each node type enforces appropriate temporal/spatial hierarchy levels
- Automatic detection of coordinate mismatches and data inconsistencies

**Data Integrity Checks**:

- Time series monotonicity validation
- Consistent array dimensions across modalities
- Missing data detection and reporting

**Status Reporting**:

- Comprehensive data availability matrices
- Excel export with visual indicators (✅/❌)
- Hierarchical completeness assessment

## Plotting Pipeline Architecture

```text
Macros (Top Level)
    ↓
Decorator (Middle Layer)
    ↓
Ax Plotter (Axis Management)
    ↓
Unit Plotter (Bottom Level)
```

### Architecture Overview

**Data Flow**: High-level plotting requests flow from **Macros** → **Decorators** → **Ax Plotters** → **Unit Plotters**, with each layer handling specific aspects of the visualization pipeline.

### Layer Components

#### 1. Macros (`kitchen/plotter/macros/`)

**Purpose**: High-level plotting functions that define complete visualization workflows for common analysis patterns.

**Functionality**:

- Orchestrate complex multi-panel figures
- Handle data selection and organization
- Define standard visualization layouts (session overviews, trial averages, FOV comparisons)
- Manage file naming and output paths

````python
def session_overview(session_node: Session, plot_manual: PlotManual):
    """ Flat view of a session node """
    session_name = get_node_name(session_node)
    session_dataset = DataSet(name=session_name, nodes=[session_node])
    default_style(
        mosaic_style=[[session_name,],],
        content_dict={
            session_name: (
                partial(flat_view, plot_manual=plot_manual),
                session_dataset)
        },
        figsize=(5, 2),
        save_path=routing.default_fig_path(session_dataset, "SessionOverview_{}.png"),
    )
````

#### 2. Decorator (`kitchen/plotter/decorators/`)

**Purpose**: Middle layer responsible for figure styling, layout management, and coordinate system setup.

**Functionality**:

- Create matplotlib figure and axis objects using mosaic layouts
- Apply consistent styling (fonts, colors, spacing)
- Coordinate multiple plotting functions using coroutines
- Handle progressive plot stacking with automatic y-offset management
- Manage figure saving and display

**Key Features**:

- **Coroutine-based plotting**: Synchronizes multiple plot functions for proper vertical stacking
- **Mosaic layouts**: Flexible subplot arrangements for complex multi-panel figures
- **Progressive rendering**: Automatic spacing and alignment of plot elements

#### 3. Ax Plotter (`kitchen/plotter/ax_plotter/`)

**Purpose**: Axis-level plotting coordination that manages data synchronization and determines plot organization.

**Functionality**:

- Synchronize temporal alignment across data modalities
- Coordinate multiple data streams (neural, behavioral, timeline)
- Manage plot ordering and y-offset progression
- Handle different visualization modes (flat view vs. stack view)

**Key Functions**:

- `flat_view()`: Single node visualization with all modalities
- `stack_view()`: Multi-node overlaid visualization with event synchronization

#### 4. Unit Plotter (`kitchen/plotter/unit_plotter/`)

**Purpose**: Bottom-level plotting functions that handle individual data modality visualization.

**Functionality**:

- Render specific data types (fluorescence, behavior, events)
- Apply modality-specific styling and scaling
- Handle both single and multi-trace plotting
- Manage y-axis labeling and tick marks
- Support statistical overlays (mean ± variance)

### Configuration & Control

**PlotManual**: Central configuration object that controls which data modalities are included in visualizations:

**Plotting Parameters**: Standardized scaling and styling parameters ensure consistent visualization across different data types and experimental conditions.

## Key Classes & Components

### Hierarchical Node Classes

**Node Base Class** (`kitchen.structure.hierarchical_data_structure.Node`):

- Enforces coordinate validation for temporal/spatial hierarchy levels
- Provides data segmentation and alignment methods
- Supports functional operations (filtering, transformation)

**Specialized Node Types**:

- `Trial`: Individual trial/chunk data (temporal: chunk, spatial: cell)
- `CellSession`: Cell data aggregated at session level (temporal: session, spatial: cell)
- `Session`: Session-level FOV data (temporal: session, spatial: fov)
- `Cell`: Individual cell across experimental template (temporal: template, spatial: cell)
- `FovDay`: FOV data for specific day (temporal: day, spatial: fov)
- `Fov`: FOV across experimental template (temporal: template, spatial: fov)
- `Mice`: Individual animal data (temporal: template, spatial: mice)
- `Cohort`: Experimental cohort data (temporal: template, spatial: cohort)

### Data Container Classes

**NeuralData** (`kitchen.structure.neural_data_structure.NeuralData`):
Central container integrating all experimental modalities:

```python
@dataclass
class NeuralData:
    # Behavioral modalities
    position: Optional[Events] = None
    locomotion: Optional[Events] = None
    lick: Optional[Events] = None
    pupil: Optional[TimeSeries] = None
    tongue: Optional[TimeSeries] = None
    whisker: Optional[TimeSeries] = None

    # Neural data
    fluorescence: Optional[Fluorescence] = None
    potential: Optional[Potential] = None

    # Experimental timeline
    timeline: Optional[Timeline] = None
```

**Core Data Types**:

- `TimeSeries`: Base class for continuous temporal data with validation
- `Events`: Discrete event data with timestamps and values
- `Fluorescence`: Calcium imaging data with motion correction and cell metadata
- `Potential`: Electrophysiological recordings with spike detection and filtering
- `Timeline`: Experimental event sequences with supported event validation

### DataSet Management

**DataSet Class** (`kitchen.structure.hierarchical_data_structure.DataSet`):

- Manages collections of hierarchical nodes as partially ordered sets
- Fast lookup by node type with internal indexing
- Supports functional operations (select, filter, map)
- Provides status reporting and data completeness analysis

**Key Methods**:

```python
# Node selection by type
fov_nodes = dataset.select("fov")
trial_nodes = dataset.select("trial")

# Functional filtering
active_cells = dataset.filter(lambda node: node.data.fluorescence is not None)

# Status reporting
dataset.status(save_path="report.xlsx")
```

### Configuration & Routing

**Path Management** (`kitchen.configs.routing`):

- Automatic file system path generation based on node coordinates
- Template-based data organization with fallback strategies
- Configurable root paths for data, figures, and test outputs

**Settings Modules** (`kitchen.settings/`):

- `behavior.py`: Behavioral data processing parameters
- `fluorescence.py`: Calcium imaging analysis settings
- `loaders.py`: Data file search mode configuration
- `timeline.py`: Supported experimental events and alignment parameters
- `trials.py`: Trial extraction and alignment windows

### Video Processing Pipeline

**Format Conversion** (`kitchen.video.format_converter`):

- Automated video format conversion (H.264 → AVI)
- Batch processing with progress tracking
- FFmpeg integration with quality preservation

**Feature Extraction** (`kitchen.video.custom_extraction`):

- Interactive ROI selection for behavioral features
- Optical flow analysis for motion quantification
- Real-time visualization during processing

**Specialized Extractors**:

- Pupil tracking integration with Facemap
- Whisker motion analysis via optical flow
- Tongue position tracking from video streams

## Setup & Usage

### Development Setup

```bash
# Clone the repository
git clone <repository-url>
cd Ratatouille

# Install in development mode
pip install -e .
```

### Configuration

**Data Path Setup**:
The framework expects data organized in the `ingredients/` directory following this structure:

```text
ingredients/
├── {cohort_id}/
│   ├── {mice_id}/
│   │   ├── {fov_id}/
│   │   │   ├── soma/Fall.mat          # Suite2p output
│   │   │   ├── timeline/TIMELINE_*.csv # Event timelines
│   │   │   ├── lick/LICK_*.csv        # Lick timestamps
│   │   │   ├── locomotion/LOCOMOTION_*.csv # Rotary encoder
│   │   │   └── video/VIDEO_*.avi      # Behavioral videos
```

**Path Configuration** (`kitchen/configs/routing.py`):

- `ROOT_PATH`: Project root directory
- `DATA_PATH`: Raw data location (`ingredients/`)
- `FIGURE_PATH`: Analysis outputs (`cuisine/`)
- `TEST_PATH`: Test suite location (`critic/`)

**Data File Search Modes** (`kitchen/settings/loaders.py`):
The framework supports two data file organization modes:

- **Strict Mode** (default): Data files must be organized in type-specific subdirectories:
  - Lick data → `lick/` directory
  - Fluorescence data → `soma/` directory
  - Pupil data → `pupil/` directory
  - Timeline data → `timeline/` directory
  - Locomotion data → `locomotion/` directory

- **Data Hodgepodge Mode**: Files can be located anywhere within the session directory structure. Enable by setting `DATA_HODGEPODGE_MODE = True`. This mode provides more flexibility but requires unique file naming conventions to identify data types (e.g., `LICK_*.csv`, `TIMELINE_*.csv`, `Fall.mat`).

### Basic Workflows

**Complete Data Loading**:

```python
import kitchen.loader.two_photon_loader as hier_loader

# Load full experimental hierarchy
dataset = hier_loader.cohort_loader(
    template_id="RandPuff",
    cohort_id="HighFreqImaging_202507"
)

# Access different hierarchy levels
sessions = dataset.select("session")
trials = dataset.select("trial")
cells = dataset.select("cellsession")
```

**Data Analysis Example**:

```python
# Extract trial-aligned data
from kitchen.settings.timeline import TRIAL_ALIGN_EVENT_DEFAULT

# Get trials aligned to stimulus onset
puff_trials = [trial for trial in trials
               if any(event in trial.data.timeline.v
                     for event in TRIAL_ALIGN_EVENT_DEFAULT[0])]

# Analyze fluorescence responses
for trial in puff_trials:
    if trial.data.fluorescence is not None:
        # Access detrended fluorescence
        detrend_f = trial.data.fluorescence.detrend_f
        # Access ΔF/F₀
        df_f0 = trial.data.fluorescence.df_f0
```

**Visualization**:

```python
from kitchen.plotter.macros.basic_macros import session_overview, fov_overview, fov_trial_avg_default
from kitchen.plotter.plotting_manual import PlotManual

# Configure what data modalities to plot
plot_manual = PlotManual(
    timeline=True,      # Experimental events
    fluorescence=True,  # Calcium imaging data
    lick=True,         # Licking behavior
    locomotion=True,   # Movement data
    pupil=True,        # Pupil diameter
    whisker=True       # Whisker motion
)

# Generate session overview plots
for session in dataset.select("session"):
    session_overview(session, plot_manual=plot_manual)

# Generate FOV overview across sessions
for fov in dataset.select("fov"):
    fov_overview(fov, dataset, plot_manual=plot_manual)
    fov_trial_avg_default(fov, dataset, plot_manual=plot_manual)
```

**Video Processing**:

```python
import kitchen.video.format_converter as format_converter
import kitchen.video.custom_extraction as custom_extraction

# Convert video formats (including TIFF stacks)
format_converter.dataset_interface_h264_2_avi(dataset)
format_converter.stack_tiff_to_video(data_path)  # New: TIFF stack conversion

# Extract behavioral features
custom_extraction.default_collection(dataset)
```

### Validation & Quality Control

**Data Integrity Checks**:

```python
# Generate comprehensive status report
dataset.status(save_path="data_status.xlsx")

# Validate data completeness
for node in dataset.nodes:
    status = node.data.status()
    print(f"{node.coordinate}: {status}")
```

## Dependencies

### Core Dependencies

**Scientific Computing**:

- `numpy>=1.24.0`: Array operations and numerical computing
- `pandas>=2.0.0`: Data manipulation and CSV/Excel I/O
- `scipy>=1.10.0`: Statistical functions and signal processing

**Computer Vision & Video**:

- `opencv-python>=4.7.0`: Video processing and optical flow analysis
- FFmpeg (external): Video format conversion and compression

**Utilities**:

- `tqdm>=4.65.0`: Progress bars for long-running operations
- `send2trash>=1.8.0`: Safe file deletion during video processing
- `typing-extensions>=4.5.0`: Enhanced type hints for Python <3.12

### Optional Dependencies

**Visualization** (for plotting functionality):

- `matplotlib`: Figure generation and data visualization
- `seaborn`: Statistical plotting enhancements

**External Tools**:

- **Suite2p**: Calcium imaging analysis (generates Fall.mat files)
- **Facemap**: Pupil tracking and facial feature extraction
- **Arduino**: Timeline event generation and TTL synchronization

### Development Dependencies

```bash
# Install development dependencies
pip install pytest pytest-cov black flake8 mypy

# Run code formatting
black kitchen/ critic/

# Type checking
mypy kitchen/

# Linting
flake8 kitchen/
```

## License

MIT License - see LICENSE file for details.

## Contact

For bug reports, contact Max (<<EMAIL>>)
